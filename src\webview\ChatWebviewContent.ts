import * as vscode from 'vscode';

/**
 * 管理聊天 WebView 的 HTML 内容
 */
export class ChatWebviewContent {
	/**
	 * 生成聊天 WebView 的 HTML 内容
	 * @param webview WebView 实例
	 * @returns HTML 字符串
	 */
	public static getHtmlForWebview(webview: vscode.Webview): string {
		return `<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8">
				<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src ${webview.cspSource} 'unsafe-inline';">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>AI Chat</title>
				<style>
					* {
						margin: 0;
						padding: 0;
						box-sizing: border-box;
					}

					body {
						font-family: var(--vscode-font-family);
						font-size: var(--vscode-font-size, 13px);
						height: 100vh;
						overflow: hidden;
						background-color: var(--vscode-sideBar-background);
						color: var(--vscode-foreground);
					}

					.chat-container {
						display: flex;
						flex-direction: column;
						height: 100vh;
						background-color: var(--vscode-sideBar-background);
					}

					.messages-container {
						flex: 1;
						overflow-y: auto;
						padding: 8px;
						background-color: var(--vscode-editor-background);
					}

					.input-container {
						display: flex;
						padding: 8px;
						border-top: 1px solid var(--vscode-panel-border);
						background-color: var(--vscode-sideBar-background);
						gap: 8px;
					}

					#messageInput {
						flex: 1;
						padding: 8px 12px;
						border: 1px solid var(--vscode-input-border);
						border-radius: 2px;
						font-size: var(--vscode-font-size, 13px);
						font-family: var(--vscode-font-family);
						outline: none;
						background-color: var(--vscode-input-background);
						color: var(--vscode-input-foreground);
						height: 32px;
						box-sizing: border-box;
					}

					#messageInput:focus {
						border-color: var(--vscode-focusBorder);
						box-shadow: 0 0 0 1px var(--vscode-focusBorder);
					}

					#sendBtn {
						padding: 8px 16px;
						background-color: var(--vscode-button-background);
						color: var(--vscode-button-foreground);
						border: none;
						border-radius: 2px;
						font-size: var(--vscode-font-size, 13px);
						font-family: var(--vscode-font-family);
						font-weight: 400;
						cursor: pointer;
						transition: background-color 0.2s;
						height: 32px;
						box-sizing: border-box;
					}

					#sendBtn:hover {
						background-color: var(--vscode-button-hoverBackground);
					}

					#sendBtn:disabled {
						background-color: var(--vscode-button-secondaryBackground);
						color: var(--vscode-button-secondaryForeground);
						cursor: not-allowed;
						opacity: 0.6;
					}

					/* Scrollbar styling */
					.messages-container::-webkit-scrollbar {
						width: 6px;
					}

					.messages-container::-webkit-scrollbar-track {
						background: transparent;
					}

					.messages-container::-webkit-scrollbar-thumb {
						background-color: var(--vscode-scrollbarSlider-background);
						border-radius: 3px;
					}

					.messages-container::-webkit-scrollbar-thumb:hover {
						background-color: var(--vscode-scrollbarSlider-hoverBackground);
					}

					/* Message styles */
					.message {
						margin-bottom: 8px;
						width: 100%;
						word-wrap: break-word;
					}

					.message-user {
						display: flex;
						flex-direction: column;
						align-items: flex-end;
					}

					.message-ai {
						display: flex;
						flex-direction: column;
						align-items: flex-start;
					}

					.message-content {
						padding: 8px 12px;
						border-radius: 3px;
						width: 100%;
						line-height: 1.4;
						font-size: var(--vscode-font-size, 13px);
						font-family: var(--vscode-font-family);
						display: flex;
						align-items: flex-start;
						position: relative;
					}

					.message-icon {
						margin-right: 8px;
						width: 16px;
						height: 16px;
						flex-shrink: 0;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 12px;
					}

					.message-text {
						flex: 1;
					}

					.message-user .message-content {
						background-color: var(--vscode-list-activeSelectionBackground);
						color: var(--vscode-list-activeSelectionForeground);
						border: 1px solid var(--vscode-widget-border);
					}

					.message-ai .message-content {
						background-color: var(--vscode-list-hoverBackground);
						color: var(--vscode-foreground);
						border: 1px solid var(--vscode-widget-border);
					}

					.message-time {
						font-size: 11px;
						font-family: var(--vscode-font-family);
						color: var(--vscode-descriptionForeground);
						margin-top: 8px;
						opacity: 0.7;
					}

					.message-ai .message-time {
						margin-left: 24px;
					}

					.message-user .message-time {
						margin-right: 24px;
					}

					/* Typing indicator styles */
					.typing-indicator {
						display: flex;
						align-items: center;
						margin-bottom: 8px;
						padding: 8px 12px;
						background-color: var(--vscode-list-hoverBackground);
						border: 1px solid var(--vscode-widget-border);
						border-radius: 3px;
						width: 100%;
						font-size: var(--vscode-font-size, 13px);
						font-family: var(--vscode-font-family);
						color: var(--vscode-descriptionForeground);
						font-style: italic;
					}

					.typing-indicator::after {
						content: '';
						display: inline-block;
						width: 4px;
						height: 4px;
						border-radius: 50%;
						background-color: var(--vscode-descriptionForeground);
						margin-left: 8px;
						animation: typing-pulse 1.5s infinite;
					}

					@keyframes typing-pulse {
						0%, 60%, 100% { opacity: 0.3; }
						30% { opacity: 1; }
					}

					/* Loading state for send button */
					.send-btn-loading {
						opacity: 0.7;
						cursor: not-allowed;
					}

					.send-btn-loading::after {
						content: '';
						display: inline-block;
						width: 12px;
						height: 12px;
						border: 2px solid transparent;
						border-top: 2px solid currentColor;
						border-radius: 50%;
						margin-left: 8px;
						animation: button-spin 1s linear infinite;
					}

					@keyframes button-spin {
						0% { transform: rotate(0deg); }
						100% { transform: rotate(360deg); }
					}
				</style>
			</head>
			<body>
				<div class="chat-container">
					<div class="messages-container" id="messages">
						<!-- Messages will be displayed here -->
					</div>
					<div class="input-container">
						<input type="text" id="messageInput" placeholder="Type your message..." />
						<button id="sendBtn">Send</button>
					</div>
				</div>

				<script>
					// Message display functions
					function addMessage(content, isUser = false) {
						const messagesContainer = document.getElementById('messages');
						const messageDiv = document.createElement('div');
						messageDiv.className = isUser ? 'message message-user' : 'message message-ai';

						const messageContent = document.createElement('div');
						messageContent.className = 'message-content';

						// Add message icon
						const messageIcon = document.createElement('div');
						messageIcon.className = 'message-icon';
						messageIcon.textContent = isUser ? '👤' : '🤖';

						// Add message text container
						const messageText = document.createElement('div');
						messageText.className = 'message-text';
						messageText.textContent = content;

						messageContent.appendChild(messageIcon);
						messageContent.appendChild(messageText);

						const messageTime = document.createElement('div');
						messageTime.className = 'message-time';
						messageTime.textContent = new Date().toLocaleTimeString();

						messageDiv.appendChild(messageContent);
						messageDiv.appendChild(messageTime);
						messagesContainer.appendChild(messageDiv);

						scrollToBottom();
					}

					function scrollToBottom() {
						if (autoScroll) {
							const container = document.getElementById('messages');
							container.scrollTo({
								top: container.scrollHeight,
								behavior: 'smooth'
							});
						}
					}

					// Settings panel functions
					function toggleSettings() {
						settingsVisible = !settingsVisible;
						if (settingsPanel) {
							settingsPanel.style.display = settingsVisible ? 'block' : 'none';
						}
					}

					function updateFontSize() {
						if (fontSizeRange && fontSizeValue) {
							const size = fontSizeRange.value;
							fontSizeValue.textContent = size + 'px';
							document.body.style.setProperty('--custom-font-size', size + 'px');

							// Update message font sizes
							const messages = document.querySelectorAll('.message-content');
							messages.forEach(msg => {
								msg.style.fontSize = size + 'px';
							});
						}
					}

					function updateAutoScroll() {
						if (autoScrollCheck) {
							autoScroll = autoScrollCheck.checked;
						}
					}

					// Typing indicator functions
					function showTypingIndicator() {
						const indicator = document.createElement('div');
						indicator.className = 'typing-indicator';
						indicator.id = 'typingIndicator';
						indicator.innerHTML = '🤖 AI is typing...';
						document.getElementById('messages').appendChild(indicator);
						scrollToBottom();
						return indicator;
					}

					function removeTypingIndicator() {
						const indicator = document.getElementById('typingIndicator');
						if (indicator) {
							indicator.remove();
						}
					}

					// Input and send functionality
					const messageInput = document.getElementById('messageInput');
					const sendBtn = document.getElementById('sendBtn');

					// Settings functionality (for settings panel when triggered by toolbar)
					let settingsPanel = null;
					let fontSizeRange = null;
					let fontSizeValue = null;
					let autoScrollCheck = null;

					let settingsVisible = false;
					let autoScroll = true;

					function sendMessage() {
						const content = messageInput.value.trim();
						if (!content) return;

						// Add user message
						addMessage(content, true);
						messageInput.value = '';

						// Show loading state
						sendBtn.disabled = true;
						sendBtn.className = 'send-btn-loading';
						sendBtn.textContent = 'Sending';

						// Show typing indicator
						const typingIndicator = showTypingIndicator();

						// Simulate AI reply (delay 1.5-3 seconds for more realistic feel)
						const delay = 1500 + Math.random() * 1500;
						setTimeout(() => {
							// Remove typing indicator
							removeTypingIndicator();

							const aiResponses = [
								'That\\'s an interesting question! Let me think about this...',
								'I\\'d be happy to help you with that. Here\\'s what I suggest:',
								'Great question! Based on what you\\'ve shared, I think...',
								'I understand what you\\'re asking. Let me provide some guidance:',
								'Could you provide more details? That would help me give you a better answer.',
								'That\\'s a great point to consider. Here\\'s my perspective:',
								'Let me break this down for you step by step.',
								'Interesting! I\\'ve seen similar situations before. Here\\'s what usually works:',
								'Good thinking! You might also want to consider...',
								'That makes sense. Have you tried approaching it this way?'
							];
							const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];
							addMessage(randomResponse, false);

							// Reset send button
							sendBtn.disabled = false;
							sendBtn.className = '';
							sendBtn.textContent = 'Send';

							// Focus input for next message
							messageInput.focus();
						}, delay);
					}

					// Event listeners
					sendBtn.addEventListener('click', () => {
						if (canSendMessage()) {
							sendMessage();
						}
					});

					messageInput.addEventListener('keypress', (e) => {
						if (e.key === 'Enter') {
							e.preventDefault();
							if (canSendMessage()) {
								sendMessage();
							}
						}
					});

					// Listen for messages from extension
					window.addEventListener('message', event => {
						const message = event.data;
						switch (message.type) {
							case 'newChat':
								newChat();
								break;
							case 'clearChat':
								clearChat();
								break;
							case 'exportChat':
								exportChat();
								break;
							case 'showSettings':
								showSettings();
								break;
						}
					});

					// Enhanced input management
					messageInput.addEventListener('focus', () => {
						messageInput.style.borderColor = 'var(--vscode-focusBorder)';
					});

					messageInput.addEventListener('blur', () => {
						messageInput.style.borderColor = 'var(--vscode-input-border)';
					});

					// Auto-resize input and enable/disable send button based on content
					messageInput.addEventListener('input', () => {
						const hasContent = messageInput.value.trim().length > 0;
						sendBtn.disabled = !hasContent || sendBtn.classList.contains('send-btn-loading');

						// Update send button appearance based on content
						if (hasContent && !sendBtn.classList.contains('send-btn-loading')) {
							sendBtn.style.opacity = '1';
						} else if (!sendBtn.classList.contains('send-btn-loading')) {
							sendBtn.style.opacity = '0.6';
						}
					});

					// Prevent sending empty messages
					function canSendMessage() {
						return messageInput.value.trim().length > 0 && !sendBtn.disabled;
					}

					// New functions for toolbar commands
					function newChat() {
						// Clear all messages and reset to initial state
						const messagesContainer = document.getElementById('messages');
						messagesContainer.innerHTML = '';

						// Reset input
						messageInput.value = '';
						sendBtn.disabled = true;
						sendBtn.style.opacity = '0.6';

						// Add welcome message
						setTimeout(() => {
							addMessage('👋 Hello! I\\'m your AI assistant. How can I help you today?', false);
						}, 500);
					}

					function clearChat() {
						// Clear all messages
						const messagesContainer = document.getElementById('messages');
						messagesContainer.innerHTML = '';
					}

					function exportChat() {
						// Get all messages and export
						const messages = document.querySelectorAll('.message');
						let chatData = 'AI Chat Export\\\\n================\\\\n\\\\n';

						messages.forEach(message => {
							const isUser = message.classList.contains('message-user');
							const content = message.querySelector('.message-text').textContent;
							const time = message.querySelector('.message-time').textContent;

							chatData += (isUser ? 'User' : 'AI') + ' [' + time + ']: ' + content + '\\\\n\\\\n';
						});

						// Send data to extension for file save
						if (window.parent) {
							window.parent.postMessage({
								type: 'exportChatData',
								data: chatData
							}, '*');
						}
					}

					function showSettings() {
						// Create settings panel dynamically since HTML was removed
						createSettingsPanel();
						toggleSettings();
					}

					function createSettingsPanel() {
						// Check if settings panel already exists
						if (document.getElementById('settingsPanel')) {
							return;
						}

						// Create settings panel HTML
						const settingsHTML = \`
							<div id="settingsPanel" style="position: absolute; top: 48px; right: 8px; width: 250px; background-color: var(--vscode-dropdown-background); border: 1px solid var(--vscode-widget-border); border-radius: 3px; padding: 16px; z-index: 1000; display: none;">
								<h4 style="margin: 0 0 8px 0; font-size: var(--vscode-font-size, 13px); font-family: var(--vscode-font-family); font-weight: 600; color: var(--vscode-foreground);">Settings</h4>
								<div style="margin-bottom: 8px;">
									<label for="themeSelect" style="display: block; font-size: var(--vscode-font-size, 13px); font-family: var(--vscode-font-family); color: var(--vscode-foreground); margin-bottom: 8px; font-weight: 400;">Theme:</label>
									<select id="themeSelect" style="width: 100%; padding: 8px; border: 1px solid var(--vscode-input-border); border-radius: 2px; background-color: var(--vscode-input-background); color: var(--vscode-input-foreground); font-size: var(--vscode-font-size, 13px); font-family: var(--vscode-font-family); height: 24px; box-sizing: border-box;">
										<option value="auto">Auto (Follow VS Code)</option>
										<option value="light">Light</option>
										<option value="dark">Dark</option>
									</select>
								</div>
								<div style="margin-bottom: 8px;">
									<label for="fontSizeRange" style="display: block; font-size: var(--vscode-font-size, 13px); font-family: var(--vscode-font-family); color: var(--vscode-foreground); margin-bottom: 8px; font-weight: 400;">Font Size: <span id="fontSizeValue">14px</span></label>
									<input type="range" id="fontSizeRange" min="12" max="18" value="14" step="1" style="width: 100%; padding: 8px; border: 1px solid var(--vscode-input-border); border-radius: 2px; background-color: var(--vscode-input-background); color: var(--vscode-input-foreground); font-size: var(--vscode-font-size, 13px); font-family: var(--vscode-font-family); height: 24px; box-sizing: border-box;">
								</div>
								<div style="margin-bottom: 8px;">
									<label style="display: block; font-size: var(--vscode-font-size, 13px); font-family: var(--vscode-font-family); color: var(--vscode-foreground); margin-bottom: 8px; font-weight: 400;">
										<input type="checkbox" id="autoScrollCheck" checked style="margin-right: 8px;"> Auto-scroll to new messages
									</label>
								</div>
								<button id="settingsCloseBtn" style="width: 100%; padding: 8px 16px; background-color: var(--vscode-button-secondaryBackground); color: var(--vscode-button-secondaryForeground); border: none; border-radius: 2px; font-size: var(--vscode-font-size, 13px); font-family: var(--vscode-font-family); cursor: pointer; margin-top: 8px; transition: background-color 0.2s; height: 32px; box-sizing: border-box;">Close</button>
							</div>
						\`;

						// Add to document
						document.body.insertAdjacentHTML('beforeend', settingsHTML);

						// Get references to elements
						settingsPanel = document.getElementById('settingsPanel');
						fontSizeRange = document.getElementById('fontSizeRange');
						fontSizeValue = document.getElementById('fontSizeValue');
						autoScrollCheck = document.getElementById('autoScrollCheck');
						const settingsCloseBtn = document.getElementById('settingsCloseBtn');

						// Add event listeners
						fontSizeRange.addEventListener('input', updateFontSize);
						autoScrollCheck.addEventListener('change', updateAutoScroll);
						settingsCloseBtn.addEventListener('click', toggleSettings);

						// Close settings when clicking outside
						document.addEventListener('click', (e) => {
							if (settingsVisible && settingsPanel &&
								!settingsPanel.contains(e.target)) {
								toggleSettings();
							}
						});
					}

					// Initialize the interface
					function initializeInterface() {
						newChat();
					}

					// Add some example messages when the page loads
					window.addEventListener('load', initializeInterface);
				</script>
			</body>
			</html>`;
	}
}