import * as vscode from 'vscode';
import { ChatWebviewContent } from '../webview/ChatWebviewContent';

/**
 * ChatViewProvider 类实现 WebviewViewProvider 接口
 * 负责管理聊天 WebView 的生命周期和消息处理
 */
export class ChatViewProvider implements vscode.WebviewViewProvider {
	public static readonly viewType = 'ai-chat-view';
	private _view?: vscode.WebviewView;

	constructor(private readonly _extensionUri: vscode.Uri) {}

	/**
	 * 解析 WebView 视图
	 */
	public resolveWebviewView(
		webviewView: vscode.WebviewView,
		context: vscode.WebviewViewResolveContext,
		_token: vscode.CancellationToken,
	) {
		console.log('resolveWebviewView called for:', ChatViewProvider.viewType);

		this._view = webviewView;

		webviewView.webview.options = {
			// Allow scripts in the webview
			enableScripts: true,
			localResourceRoots: [
				this._extensionUri
			]
		};

		// 使用 ChatWebviewContent 生成 HTML 内容
		webviewView.webview.html = ChatWebviewContent.getHtmlForWebview(webviewView.webview);
		console.log('WebView HTML set successfully');

		// Listen for messages from the webview
		webviewView.webview.onDidReceiveMessage(
			message => {
				switch (message.type) {
					case 'exportChatData':
						// Handle chat export
						this.handleChatExport(message.data);
						break;
					case 'showInfo':
						vscode.window.showInformationMessage(message.text);
						break;
				}
			}
		);
	}

	/**
	 * 向 WebView 发送消息
	 */
	public sendMessage(message: any) {
		if (this._view) {
			this._view.webview.postMessage(message);
		}
	}

	/**
	 * 处理聊天记录导出
	 */
	private async handleChatExport(chatData: string) {
		try {
			const uri = await vscode.window.showSaveDialog({
				defaultUri: vscode.Uri.file('chat-export.txt'),
				filters: {
					'Text files': ['txt'],
					'All files': ['*']
				}
			});

			if (uri) {
				await vscode.workspace.fs.writeFile(uri, Buffer.from(chatData, 'utf8'));
				vscode.window.showInformationMessage(`聊天记录已导出到: ${uri.fsPath}`);
			}
		} catch (error) {
			vscode.window.showErrorMessage(`导出失败: ${error}`);
		}
	}
}
